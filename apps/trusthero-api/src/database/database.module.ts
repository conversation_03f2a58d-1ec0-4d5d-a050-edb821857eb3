import { Module } from '@nestjs/common';
import { ModelDefinition, MongooseModule } from '@nestjs/mongoose';
import { ConfigService } from '@libs/common/config';
import { EnvironmentVariables } from '../app/app.config';

// TODO: improve naming and file structure
// TODO: use global mongo models

@Module({
  imports: [
    MongooseModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService<EnvironmentVariables>) => ({
        uri: config.get('MONGO_URI'),
      }),
    }),
  ],
})
export class DbModule {
  // Redefine MongooseModule.forFeature method
  static forFeature(models: ModelDefinition[]) {
    return MongooseModule.forFeature(models);
  }
}
