{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@libs/common/*": ["libs/common/src/*"], "@libs/pino-logger": ["libs/pino-logger/src/index.ts"], "@libs/swagger": ["libs/swagger/src/index.ts"], "@models/*": ["models/src/*"]}}, "exclude": ["node_modules", "tmp"]}