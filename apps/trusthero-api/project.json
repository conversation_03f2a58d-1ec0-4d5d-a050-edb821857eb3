{"name": "@apps/trusthero-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/trusthero-api/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["--node-env=production"], "cwd": "apps/trusthero-api"}, "configurations": {"development": {"args": ["--node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@apps/trusthero-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@apps/trusthero-api:build:development"}, "production": {"buildTarget": "@apps/trusthero-api:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}