import { HttpStatus } from '@nestjs/common';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const CatSchema = z.object({
  id: z.string().describe('Cat id'),
  name: z.string().min(2, 'Name must be at least 2 characters').describe('Cat name'),
  age: z.number().nonnegative().describe('Cat age'),
  breed: z.string().describe('Cat breed'),
});

export type CatModel = z.infer<typeof CatSchema>;

export class CatDto extends createZodDto(CatSchema) {}

// TODO: move to common
export const createResponseDto = <T extends z.ZodTypeAny>(schema: T) => {
  return createZodDto(
    z.object({
      success: z.literal(true),
      status: z.enum(HttpStatus).default(HttpStatus.OK),
      data: schema,
      message: z.string(),
    }),
  );
};

export const CatResponseDto = createResponseDto(CatSchema);
export const CatArrayResponseDto = createResponseDto(CatSchema.array());
