{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug trusthero-api with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "trusthero-api"], "env": {"NODE_OPTIONS": "--inspect=9229"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/trusthero-api/dist/**/*.(m|c|)js", "!**/node_modules/**"]}]}