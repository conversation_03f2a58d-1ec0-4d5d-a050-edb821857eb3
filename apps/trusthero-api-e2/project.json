{"name": "@apps/trusthero-api-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["@apps/trusthero-api"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/trusthero-api-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["@apps/trusthero-api:build", "@apps/trusthero-api:serve"]}}}