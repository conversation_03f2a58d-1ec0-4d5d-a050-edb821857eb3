import { Modu<PERSON> } from '@nestjs/common';
import { DbModule, CatEntitySchema } from '../../database';
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';

@Module({
  imports: [
    DbModule, //
    DbModule.forFeature([{ name: 'Cat', schema: CatEntitySchema }]),
  ],
  providers: [CatsService],
  controllers: [CatsController],
})
export class CatsModule {}
