version: "3.8"

services:
  trusthero-api:
    build:
      context: ../../
      dockerfile: tools/docker/trusthero-api.dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    env_file:
      - ../../apps/trusthero-api/.env
    restart: unless-stopped

  # Placeholder for future microservices
  # trusthero-auth:
  #   build:
  #     context: ../../
  #     dockerfile: tools/docker/trusthero-auth.dockerfile
  #   ports:
  #     - "3001:3001"
  #   environment:
  #     - NODE_ENV=production
  #     - PORT=3001
  #   restart: unless-stopped
