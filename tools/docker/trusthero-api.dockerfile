# Multi-stage Dockerfile for trusthero-api
# Stage 1: Build stage
FROM node:22.20.0-alpine AS builder
# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package*.json ./

# Install dependencies (including dev dependencies for build)
RUN npm ci

# Copy configuration files
COPY nx.json ./
COPY tsconfig.base.json ./

# Copy dependencies
COPY libs ./libs
COPY models ./models

# Copy source code
# .env is copied by this step
COPY apps/trusthero-api ./apps/trusthero-api

# Build the application
RUN npx nx build trusthero-api --prod

# Stage 2: Production stage
FROM node:22.20.0-alpine AS production

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

# Set working directory
WORKDIR /app

# Copy package.json for production dependencies
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder --chown=nestjs:nodejs /app/dist/apps/trusthero-api ./

# Switch to non-root user
USER nestjs

# Expose the port the app runs on
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/api', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
CMD ["node", "main.js"]