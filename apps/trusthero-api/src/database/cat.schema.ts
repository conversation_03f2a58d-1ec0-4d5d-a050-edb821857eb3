import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { VirtualId } from '@libs/common/database/mongo';
import { CatModel } from '@models/cats';

@Schema({ collection: 'cats' })
export class Cat implements CatModel {
  @VirtualId()
  id: string;

  @Prop()
  name: string;

  @Prop()
  age: number;

  @Prop()
  breed: string;
}

export type CatDocument = HydratedDocument<Cat>;

export const CatEntitySchema = SchemaFactory.createForClass(Cat);
