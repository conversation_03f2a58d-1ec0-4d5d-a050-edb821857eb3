/* eslint-disable no-console */
import chalk from 'chalk';

type BootstrapLogOptions = {
  appName?: string;
  appUrl: string;
  nodeEnv: string;
  swaggerUrl?: string;
};

export const logAppStartup = ({ appName, appUrl, nodeEnv, swaggerUrl }: BootstrapLogOptions) => {
  const width = process.stdout.columns || 64;
  const pattern = '=';
  console.log(chalk.bold.green(pattern.repeat(width)));
  console.log(chalk.bold.green(`Application ${appName} is running on: ${appUrl}`));
  console.log(chalk.bold.green(`Node env: ${nodeEnv}`));
  if (swaggerUrl) {
    console.log(chalk.bold.green(`Swagger: ${swaggerUrl}`));
  }
  console.log(chalk.bold.green(pattern.repeat(width)));
  console.log();
};
