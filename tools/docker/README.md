# Docker Configuration

## Files

- `trusthero-api.dockerfile` - Dockerfile for the main API service
- `docker-compose.yml` - Docker Compose configuration for local development

## Usage

### Local Development

To run the API service locally using Docker:

```bash
# From the project root
docker-compose -f tools/docker/docker-compose.yml up --build
```

### Digital Ocean Apps Deployment

The project is configured to deploy to Digital Ocean Apps using the Dockerfile located at `tools/docker/trusthero-api.dockerfile`.

Configuration is specified in `.do/app.yaml` in the project root.

### Building Individual Services

To build the API service:

```bash
# From the project root
docker build -f tools/docker/trusthero-api.dockerfile -t trusthero-api .
```

To run the built image:

```bash
docker run -p 3000:3000 -e NODE_ENV=production trusthero-api
```
