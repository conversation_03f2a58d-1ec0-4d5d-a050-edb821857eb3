import { Module } from '@nestjs/common';
import { ConfigModuleOptions, ConfigModule as NestJsConfigModule } from '@nestjs/config';
import { ZodType } from 'zod';
import { generateAppEnvFilePaths } from './config.helpers';

@Module({})
export class ConfigModule {
  static forRoot<T extends object>(params: { schema: ZodType<T> } & ConfigModuleOptions) {
    const { schema, envFilePath, ...restOptions } = params;

    return NestJsConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      envFilePath: envFilePath || generateAppEnvFilePaths(),
      validate: (config) => schema.parse(config),
      ...restOptions,
    });
  }
}
