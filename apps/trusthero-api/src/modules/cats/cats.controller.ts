import { Body, Controller, Delete, Get, HttpStatus, Param, Post } from '@nestjs/common';
import { ZodResponse, ZodSerializerDto } from 'nestjs-zod';
import { HttpResponse } from '@libs/common/api';
import { ApiOperation } from '@libs/swagger';
import { CatArrayResponseDto, CatDto, CatResponseDto, CreateCatDto } from '@models/cats';
import { CatsService } from './cats.service';

@Controller('cats')
export class CatsController {
  constructor(private readonly catsService: CatsService) {}

  @Post()
  @ApiOperation({ summary: 'Create cat' })
  @ZodResponse({
    type: CatResponseDto,
    status: HttpStatus.CREATED,
  })
  async createCat(@Body() dto: CreateCatDto) {
    const cat = await this.catsService.create(dto);
    return new HttpResponse({ data: cat });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete cat' })
  // TODO: add empty zod response
  async deleteCat(@Param('id') catId: string) {
    await this.catsService.delete(catId);
    return new HttpResponse({ message: 'Cat deleted' });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get cat' })
  @ZodResponse({ type: CatResponseDto })
  async getCat(@Param('id') catId: string) {
    const cat = await this.catsService.findOne(catId);
    return new HttpResponse({ data: cat });
  }

  @Get()
  @ApiOperation({ summary: 'Get all cats' })
  @ZodResponse({ type: CatArrayResponseDto })
  getAllCats() {
    const cats = this.catsService.findAll();
    return new HttpResponse({ data: cats });
  }
}
