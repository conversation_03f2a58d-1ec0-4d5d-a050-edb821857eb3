import { Params } from 'nestjs-pino';
import { randomUUID } from 'node:crypto';
import { IncomingMessage } from 'node:http';
import { ConfigService, isDevelopment } from '@libs/common/config';

export const usePinoHttpOptions = (options: { configService: ConfigService }): Params['pinoHttp'] => {
  const { configService } = options;

  const baseOptions = {
    level: configService.get('LOG_LEVEL'),
    genReqId: (req: IncomingMessage) => req.headers['x-correlation-id'] || randomUUID(),
  };

  // Use pino-pretty for development
  if (isDevelopment()) {
    return {
      ...baseOptions,
      transport: {
        target: 'pino-pretty',
        options: {
          translateTime: 'SYS:standard',
          colorize: true,
          singleLine: true,
          ignore:
            'pid,hostname,context,req.headers,req.params,req.query,req.remoteAddress,req.remotePort,res',
        },
      },
    } as Params['pinoHttp'];
  }

  return baseOptions;
};
