import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateCatDto } from '@models/cats';
import { Cat } from '../../database';

@Injectable()
export class CatsService {
  constructor(@InjectModel(Cat.name) private cats: Model<Cat>) {}

  async create(createCatDto: CreateCatDto): Promise<Cat> {
    const createdCat = new this.cats(createCatDto);
    return createdCat.save();
  }

  async delete(catId: string): Promise<void> {
    await this.cats.findByIdAndDelete(catId).exec();
  }

  async findOne(catId: string): Promise<Cat> {
    return this.cats.findById(catId).exec();
  }

  async findAll(): Promise<Cat[]> {
    return this.cats.find().exec();
  }
}
