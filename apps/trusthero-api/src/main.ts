import { LogLevel } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { cleanupOpenApiDoc } from 'nestjs-zod';
import { ConfigService, isDevelopment } from '@libs/common/config';
import { logAppStartup } from '@libs/common/utils/bootstrap';
import { Logger, LoggerErrorInterceptor } from '@libs/pino-logger';
import { DocumentBuilder, SwaggerModule } from '@libs/swagger';
import { AppModule } from './app/app.module';

// TODO: go on with configuring new version of https://github.com/BenLorantfy/nestjs-zod#readme

// TODO: improve imports for nested paths like ../../../../

const configureLogs = () => {
  const levels = ['warn', 'error', 'fatal'];
  if (isDevelopment()) levels.push('debug', 'log');
  return levels as LogLevel[];
};

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: configureLogs(),
  });
  app.enableShutdownHooks();

  const config = app.get(ConfigService);

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Configure logger
  app.useGlobalInterceptors(new LoggerErrorInterceptor());

  const isSwaggerEnabled = config.get('SWAGGER_ENABLED');
  if (isSwaggerEnabled) {
    const openApiConfig = new DocumentBuilder()
      .setTitle('API')
      .setVersion('0.0.0')
      // .addBearerAuth({ type: 'http', scheme: 'bearer' }, ACCESS_TOKEN_SECURITY_KEY)
      .build();

    const openApi = SwaggerModule.createDocument(app, openApiConfig);
    // to see the webpage open: /swagger
    SwaggerModule.setup('swagger', app, cleanupOpenApiDoc(openApi), {
      jsonDocumentUrl: 'swagger/json',
    });
  }

  // Start server
  await app.listen(config.getOrThrow('PORT'));

  logAppStartup({
    appName: 'trusthero-api',
    appUrl: await app.getUrl(),
    nodeEnv: process.env.NODE_ENV,
    swaggerUrl: isSwaggerEnabled ? `${await app.getUrl()}/swagger` : undefined,
  });

  // Configure logger (next logs will be handled by pino logger)
  app.useLogger(app.get(Logger));
}

void bootstrap();
