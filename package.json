{"name": "@trusthero/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"@nestjs/common": "^11.1.6", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.6", "@nestjs/mongoose": "^11.0.3", "@nestjs/platform-express": "^11.1.6", "@nestjs/swagger": "^11.2.0", "axios": "^1.12.2", "chalk": "^4.1.2", "mongoose": "^8.19.0", "nestjs-pino": "^4.4.1", "nestjs-zod": "^5.0.1", "pino": "^9.13.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "zod": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.37.0", "@nestjs/schematics": "^11.0.8", "@nestjs/testing": "^11.1.6", "@nx/eslint": "21.6.3", "@nx/eslint-plugin": "21.6.3", "@nx/jest": "21.6.3", "@nx/js": "21.6.3", "@nx/nest": "21.6.3", "@nx/node": "21.6.3", "@nx/web": "21.6.3", "@nx/webpack": "21.6.3", "@nx/workspace": "21.6.3", "@swc-node/register": "~1.11.1", "@swc/core": "~1.13.5", "@swc/helpers": "~0.5.17", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "^30.0.0", "@types/node": "24.7.0", "eslint": "^9.37.0", "eslint-config-prettier": "^10.1.8", "jest": "^30.2.0", "jest-environment-node": "^30.2.0", "jest-util": "^30.2.0", "nx": "21.6.3", "pino-pretty": "^13.1.1", "prettier": "^3.6.2", "ts-jest": "^29.4.4", "ts-node": "10.9.2", "tslib": "^2.8.1", "typescript": "~5.9.3", "typescript-eslint": "^8.45.0", "webpack-cli": "^6.0.1"}}